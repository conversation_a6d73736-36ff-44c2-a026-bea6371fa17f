package com.moego.server.business.service;

import static com.moego.common.enums.serviceAreaConst.ANY_AREA;

import com.google.common.collect.ImmutableMap;
import com.moego.common.utils.DateUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.dto.StaffWorkingAreaDayDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaDetailDTO;
import com.moego.server.business.dto.WorkingAreaDto;
import com.moego.server.business.mapper.MoeStaffWorkingAreaMapper;
import com.moego.server.business.mapperbean.MoeStaffWorkingArea;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StaffWorkingAreaService {

    @Autowired
    private MoeStaffWorkingAreaMapper moeStaffWorkingAreaMapper;

    public static final byte NORMAL_SCHEDULE_TYPE = 1;

    public static final byte WEEK_PERIOD = 7;

    public static final String INITIAL_DATE = "1970-01-01";
    public static final String ENDLESS_DATE = "9999-12-31";

    private static final Map<Integer, Function<StaffWorkingAreaDetailDTO, StaffWorkingAreaDayDetailDTO>>
            WEEK_FOR_GET_STAFF = ImmutableMap.of(
                    0,
                    StaffWorkingAreaDetailDTO::getFirstWeek,
                    1,
                    StaffWorkingAreaDetailDTO::getSecondWeek,
                    2,
                    StaffWorkingAreaDetailDTO::getThirdWeek,
                    3,
                    StaffWorkingAreaDetailDTO::getForthWeek);

    private static final Map<Integer, BiConsumer<StaffWorkingAreaDetailDTO, StaffWorkingAreaDayDetailDTO>>
            WEEK_FOR_SET_STAFF = ImmutableMap.of(
                    0,
                    StaffWorkingAreaDetailDTO::setFirstWeek,
                    1,
                    StaffWorkingAreaDetailDTO::setSecondWeek,
                    2,
                    StaffWorkingAreaDetailDTO::setThirdWeek,
                    3,
                    StaffWorkingAreaDetailDTO::setForthWeek);

    private static final Map<Integer, Function<StaffWorkingAreaDayDetailDTO, List<WorkingAreaDto>>>
            DAY_OF_WEEK_FOR_GET_STAFF = ImmutableMap.of(
                    0,
                    StaffWorkingAreaDayDetailDTO::getSunday,
                    1,
                    StaffWorkingAreaDayDetailDTO::getMonday,
                    2,
                    StaffWorkingAreaDayDetailDTO::getTuesday,
                    3,
                    StaffWorkingAreaDayDetailDTO::getWednesday,
                    4,
                    StaffWorkingAreaDayDetailDTO::getThursday,
                    5,
                    StaffWorkingAreaDayDetailDTO::getFriday,
                    6,
                    StaffWorkingAreaDayDetailDTO::getSaturday);

    /**
     * 获取单个 staff 的 working area
     *
     * @param businessId
     * @param staffId
     * @return
     */
    public StaffWorkingAreaDetailDTO getStaffWorkingAreaDetail(Integer businessId, Integer staffId) {
        return StaffWorkingAreaService.getStaffWorkingAreaDetailDTO(
                moeStaffWorkingAreaMapper.selectByBusinessIdAndStaffId(businessId, staffId));
    }

    /**
     * 获取单个 staff 的 working area。未设置时，取默认配置
     *
     * @param businessId
     * @param staffId
     * @return
     */
    public StaffWorkingAreaDetailDTO getStaffWorkingAreaDetailWithDefault(Integer businessId, Integer staffId) {
        StaffWorkingAreaDetailDTO detail = getStaffWorkingAreaDetail(businessId, staffId);
        if (detail != null) {
            return detail;
        }
        return StaffWorkingAreaService.getDefaultStaffWorkingAreaDetailDTO(businessId, staffId);
    }

    /**
     * 保存 working area 数据
     *
     */
    public void saveStaffWorkingArea(Integer companyId, StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO) {
        saveStaffWorkingArea(companyId, staffWorkingAreaDetailDTO.getBusinessId(), List.of(staffWorkingAreaDetailDTO));
    }

    /**
     *  week 变多，则 service area 就按已有的 week 的滚动复制到新增的 week，保证 service area 的规则跟之前一样
     *      例如 week1 变为 week 4，则 week 2 week 3 week 4 都跟 week 1 一样
     *      例如 week 1 + week 2 变为 week 3，则 week 3 跟 week 1 一样
     *      例如 week 1 + week 2 变为 week 4，则 week 3 跟 week 1 一样，week 4 跟 week 2 一样
     *  week 变少，则 week1/week2/week3 对应的 service area 不变
     */
    public void syncScheduleType(Long companyId, Integer businessId, Integer staffId, Byte newScheduleType) {
        StaffWorkingAreaDetailDTO existWorkingArea = getStaffWorkingAreaDetailWithDefault(businessId, staffId);
        for (int i = existWorkingArea.getScheduleType(); i < 4 && i < newScheduleType; i++) {
            int j = i % existWorkingArea.getScheduleType();
            WEEK_FOR_SET_STAFF
                    .get(i)
                    .accept(existWorkingArea, WEEK_FOR_GET_STAFF.get(j).apply(existWorkingArea));
        }
        existWorkingArea.setScheduleType(newScheduleType);
        saveStaffWorkingArea(companyId.intValue(), existWorkingArea);
    }

    /**
     * 批量保存 working area 数据
     * 1. 如果数据库中已经存在，则更新
     * 2. 如果数据库中不存在，则插入
     */
    public void saveStaffWorkingArea(
            Integer companyId, Integer businessId, List<StaffWorkingAreaDetailDTO> staffWorkingAreaDetailDTOs) {
        if (CollectionUtils.isEmpty(staffWorkingAreaDetailDTOs)) {
            return;
        }
        List<Integer> staffIds = staffWorkingAreaDetailDTOs.stream()
                .map(StaffWorkingAreaDetailDTO::getStaffId)
                .toList();
        Map<Integer, MoeStaffWorkingArea> exitsWorkingAreas =
                moeStaffWorkingAreaMapper.query(businessId, staffIds).stream()
                        .collect(
                                Collectors.toMap(MoeStaffWorkingArea::getStaffId, Function.identity(), (k1, k2) -> k2));

        List<MoeStaffWorkingArea> workingAreasToSave = staffWorkingAreaDetailDTOs.stream()
                .filter(k -> !exitsWorkingAreas.containsKey(k.getStaffId()))
                .map(k -> getMoeStaffWorkingArea(companyId, k))
                .toList();
        List<MoeStaffWorkingArea> workingAreasToUpdate = staffWorkingAreaDetailDTOs.stream()
                .filter(k -> exitsWorkingAreas.containsKey(k.getStaffId()))
                .map(k -> {
                    MoeStaffWorkingArea moeStaffWorkingArea = getMoeStaffWorkingArea(companyId, k);
                    moeStaffWorkingArea.setId(
                            exitsWorkingAreas.get(k.getStaffId()).getId());
                    return moeStaffWorkingArea;
                })
                .toList();

        if (!CollectionUtils.isEmpty(workingAreasToSave)) {
            moeStaffWorkingAreaMapper.batchInsertRecords(workingAreasToSave);
        }
        if (!CollectionUtils.isEmpty(workingAreasToUpdate)) {
            moeStaffWorkingAreaMapper.batchUpdateById(businessId, workingAreasToUpdate);
        }
    }

    /**
     * 获取部份 staff 在一段时间内的 working area
     *
     */
    public Map<Integer, Map<LocalDate, List<WorkingAreaDto>>> getStaffWorkingArea(
            Integer businessId, List<Integer> staffIdList, List<LocalDate> dateRange) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(staffIdList) || CollectionUtils.isEmpty(dateRange)) {
            return Collections.emptyMap();
        }

        List<LocalDate> dateRangeList = new ArrayList<>(dateRange);
        Collections.sort(dateRangeList);

        // 查询员工的 working area
        Map<Integer, StaffWorkingAreaDetailDTO> staffWorkingAreaDetailMap =
                moeStaffWorkingAreaMapper.query(businessId, staffIdList).stream()
                        .map(StaffWorkingAreaService::getStaffWorkingAreaDetailDTO)
                        .collect(Collectors.toMap(
                                StaffWorkingAreaDetailDTO::getStaffId,
                                staffWorkingArea -> staffWorkingArea,
                                (key1, key2) -> key2));

        // 结果集
        Map<Integer, Map<LocalDate, List<WorkingAreaDto>>> result = new ConcurrentHashMap<>(32);

        // 并行计算
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        staffIdList.forEach(staffId -> completableFutureList.add(CompletableFuture.runAsync(
                () -> result.put(
                        staffId, getEveryStaffWorkingArea(dateRangeList, staffWorkingAreaDetailMap.get(staffId))),
                ThreadPool.getExecutor())));

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();

        return result;
    }

    private Map<LocalDate, List<WorkingAreaDto>> getEveryStaffWorkingArea(
            List<LocalDate> localDateRange, StaffWorkingAreaDetailDTO staffWorkingAreaDetail) {
        Map<LocalDate, List<WorkingAreaDto>> staffResult = new ConcurrentHashMap<>(32);

        // 未设置 working area
        if (staffWorkingAreaDetail == null) {
            return staffResult;
        }
        LocalDate workingAreaStartDate = staffWorkingAreaDetail.getStartDate();
        LocalDate workingAreaEndDate = staffWorkingAreaDetail.getEndDate();

        int rangeSize = localDateRange.size();
        LocalDate dateRangeStart = localDateRange.get(0);
        LocalDate dateRangeEnd = localDateRange.get(rangeSize - 1);

        // date 不在设置的时间范围内
        if (workingAreaEndDate.isBefore(dateRangeStart) || workingAreaStartDate.isAfter(dateRangeEnd)) {
            return staffResult;
        }

        int startDateIndex = Collections.binarySearch(localDateRange, workingAreaStartDate);
        if (startDateIndex < 0) {
            startDateIndex = 0;
        }
        int endDateIndex = Collections.binarySearch(localDateRange, workingAreaEndDate);
        if (endDateIndex < 0) {
            endDateIndex = rangeSize;
        }

        // 偏移计算
        int endDateIndexReal = endDateIndex == rangeSize ? rangeSize : endDateIndex + 1;
        localDateRange.subList(startDateIndex, endDateIndexReal).forEach(currentDate -> {
            List<WorkingAreaDto> staffWorkingAreas =
                    getStaffWorkingAreaByCurrentDate(staffWorkingAreaDetail, currentDate);
            staffResult.put(
                    currentDate,
                    staffWorkingAreas == null
                            ? List.of(new WorkingAreaDto(Integer.valueOf(ANY_AREA)))
                            : staffWorkingAreas);
        });

        return staffResult;
    }

    public static List<WorkingAreaDto> getStaffWorkingAreaByCurrentDate(
            StaffWorkingAreaDetailDTO staffWorkingAreaDetail, LocalDate currentLocalDate) {
        LocalDate workingHourStartDate = staffWorkingAreaDetail.getStartDate();
        Byte scheduleType = staffWorkingAreaDetail.getScheduleType();
        int dayOfWeek = DateUtil.localDateToWeek(currentLocalDate);

        // 如果设置为每周相同时间，则直接按星期获取时间数据
        if (Objects.equals(scheduleType, NORMAL_SCHEDULE_TYPE) || scheduleType < 1) {
            return DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek).apply(staffWorkingAreaDetail.getFirstWeek());
        }

        // 计算 startDate 所在星期的星期日，也就是绝对开始时间
        LocalDate absoluteStartDate = workingHourStartDate.minusDays(DateUtil.localDateToWeek(workingHourStartDate));
        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(absoluteStartDate, currentLocalDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, WEEK_PERIOD);
        // 计算循环中第几周，0 是第一周，1 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType));

        return WEEK_FOR_GET_STAFF
                .get(weekNumber)
                .andThen(DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek))
                .apply(staffWorkingAreaDetail);
    }

    public static List<WorkingAreaDto> getStaffWorkingAreaByWeekAndDayNumber(
            int weekNumber, int dayOfWeek, StaffWorkingAreaDetailDTO staffWorkingAreaDetail) {
        return WEEK_FOR_GET_STAFF
                .get(weekNumber)
                .andThen(DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek))
                .apply(staffWorkingAreaDetail);
    }

    public List<WorkingAreaDto> getWorkingAreas(StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO) {
        List<WorkingAreaDto> workingAreaDtos = new ArrayList<>();
        if (staffWorkingAreaDetailDTO == null) {
            return workingAreaDtos;
        }
        for (int weekNumber = 0; weekNumber < WEEK_FOR_GET_STAFF.size(); weekNumber++) {
            for (int dayOfWeek = 0; dayOfWeek < DAY_OF_WEEK_FOR_GET_STAFF.size(); dayOfWeek++) {
                workingAreaDtos.addAll(WEEK_FOR_GET_STAFF
                        .get(weekNumber)
                        .andThen(DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek))
                        .apply(staffWorkingAreaDetailDTO));
            }
        }
        return workingAreaDtos;
    }

    private static StaffWorkingAreaDetailDTO getStaffWorkingAreaDetailDTO(MoeStaffWorkingArea workingArea) {
        if (workingArea == null) {
            return null;
        }
        StaffWorkingAreaDetailDTO detailDTO = new StaffWorkingAreaDetailDTO();
        detailDTO.setBusinessId(workingArea.getBusinessId());
        detailDTO.setStaffId(workingArea.getStaffId());
        detailDTO.setScheduleType(workingArea.getScheduleType());
        detailDTO.setStartDate(LocalDate.parse(workingArea.getStartDate()));
        detailDTO.setEndDate(LocalDate.parse(workingArea.getEndDate()));
        detailDTO.setFirstWeek(workingArea.getFirstWeek());
        detailDTO.setSecondWeek(workingArea.getSecondWeek());
        detailDTO.setThirdWeek(workingArea.getThirdWeek());
        detailDTO.setForthWeek(workingArea.getForthWeek());

        return detailDTO;
    }

    public static MoeStaffWorkingArea getMoeStaffWorkingArea(
            Integer companyId, StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO) {
        Integer businessId = staffWorkingAreaDetailDTO.getBusinessId();
        Integer staffId = staffWorkingAreaDetailDTO.getStaffId();

        MoeStaffWorkingArea workingArea = new MoeStaffWorkingArea();
        workingArea.setBusinessId(businessId);
        workingArea.setStaffId(staffId);
        workingArea.setScheduleType(staffWorkingAreaDetailDTO.getScheduleType());
        workingArea.setStartDate(staffWorkingAreaDetailDTO.getStartDate().toString());
        workingArea.setEndDate(staffWorkingAreaDetailDTO.getEndDate().toString());
        workingArea.setFirstWeek(staffWorkingAreaDetailDTO.getFirstWeek());
        workingArea.setSecondWeek(staffWorkingAreaDetailDTO.getSecondWeek());
        workingArea.setThirdWeek(staffWorkingAreaDetailDTO.getThirdWeek());
        workingArea.setForthWeek(staffWorkingAreaDetailDTO.getForthWeek());
        workingArea.setCompanyId(companyId);
        return workingArea;
    }

    private static List<WorkingAreaDto> getDefaultStaffWorkingAreaDayRange() {
        return List.of(new WorkingAreaDto(Integer.valueOf(ANY_AREA)));
    }

    private static StaffWorkingAreaDayDetailDTO getDefaultStaffWorkingAreaDayDetailDTO() {
        StaffWorkingAreaDayDetailDTO detailDTO = new StaffWorkingAreaDayDetailDTO();
        detailDTO.setMonday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setTuesday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setWednesday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setThursday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setFriday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setSaturday(getDefaultStaffWorkingAreaDayRange());
        detailDTO.setSunday(getDefaultStaffWorkingAreaDayRange());
        return detailDTO;
    }

    public static StaffWorkingAreaDetailDTO getDefaultStaffWorkingAreaDetailDTO(Integer businessId, Integer staffId) {
        StaffWorkingAreaDetailDTO detailDTO = new StaffWorkingAreaDetailDTO();
        detailDTO.setBusinessId(businessId);
        detailDTO.setStaffId(staffId);
        detailDTO.setScheduleType(NORMAL_SCHEDULE_TYPE);
        detailDTO.setStartDate(LocalDate.parse(INITIAL_DATE));
        detailDTO.setEndDate(LocalDate.parse(ENDLESS_DATE));
        detailDTO.setFirstWeek(getDefaultStaffWorkingAreaDayDetailDTO());
        detailDTO.setSecondWeek(getDefaultStaffWorkingAreaDayDetailDTO());
        detailDTO.setThirdWeek(getDefaultStaffWorkingAreaDayDetailDTO());
        detailDTO.setForthWeek(getDefaultStaffWorkingAreaDayDetailDTO());
        return detailDTO;
    }
}
