package com.moego.server.business.service;

import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.type.DayOfWeek;
import com.moego.common.constant.CommonConstant;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.online_booking.v1.TimeAvailabilityType;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.BookingLimitationDef;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.models.organization.v1.TimeDailySetting;
import com.moego.idl.models.organization.v1.TimeDailySettingDef;
import com.moego.idl.models.organization.v1.TimeHourSettingDef;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.UpdateStaffAvailabilityRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffOverrideDateDetailDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.StaffWorkingHourDayDetailDTO;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeStaffWorkingHourMapper;
import com.moego.server.business.mapperbean.MoeBusinessCloseDate;
import com.moego.server.business.mapperbean.MoeStaffWorkingHour;
import com.moego.server.business.web.vo.StaffByWorkingDateVO;
import com.moego.server.business.web.vo.StaffWithWorkingRangeVO;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StaffWorkingHourService {

    private final BusinessWorkingHourService businessWorkingHourService;
    private final MoeStaffWorkingHourMapper moeStaffWorkingHourMapper;
    private final MoeBusinessMapper moeBusinessMapper;
    private final IGroomingOnlineBookingClient groomingOnlineBookingClient;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc
                    .OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingService;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub obsStaffAvailabilityService;
    private final BusinessCloseDateService businessCloseDateService;
    private final IGroomingAppointmentClient iGroomingAppointmentClient;

    public static final byte NORMAL_SCHEDULE_TYPE = 1;

    public static final byte WEEK_PERIOD = 7;

    public static final String EMPTY_JSON = "{}";

    public static final String ENDLESS_DATE = "9999-12-31";

    private static Map<String, StaffTime> getStaffTimeMap(BusinessWorkingHourDayDetailDTO timeData) {
        Map<String, Function<BusinessWorkingHourDayDetailDTO, List<TimeRangeDto>>> staffTimeMapForGet = ImmutableMap.of(
                WeekUtil.KEY_OF_MONDAY,
                BusinessWorkingHourDayDetailDTO::getMonday,
                WeekUtil.KEY_OF_TUESDAY,
                BusinessWorkingHourDayDetailDTO::getTuesday,
                WeekUtil.KEY_OF_WEDNESDAY,
                BusinessWorkingHourDayDetailDTO::getWednesday,
                WeekUtil.KEY_OF_THURSDAY,
                BusinessWorkingHourDayDetailDTO::getThursday,
                WeekUtil.KEY_OF_FRIDAY,
                BusinessWorkingHourDayDetailDTO::getFriday,
                WeekUtil.KEY_OF_SATURDAY,
                BusinessWorkingHourDayDetailDTO::getSaturday,
                WeekUtil.KEY_OF_SUNDAY,
                BusinessWorkingHourDayDetailDTO::getSunday);

        return staffTimeMapForGet.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
            List<TimeRangeDto> timeRangeDtoList = e.getValue().apply(timeData);
            StaffTime tmpStaffTime = new StaffTime();
            if (CollectionUtils.isEmpty(timeRangeDtoList)) {
                tmpStaffTime.setIsSelected(java.lang.Boolean.FALSE);
            } else {
                tmpStaffTime.setTimeRange(timeRangeDtoList);
            }
            return tmpStaffTime;
        }));
    }

    public static final Map<Byte, BiConsumer<StaffWorkingHourDayDetailDTO, List<TimeRangeDto>>>
            DAY_OF_WEEK_FOR_SET_STAFF = ImmutableMap.of(
                    (byte) 0,
                    StaffWorkingHourDayDetailDTO::setSunday,
                    (byte) 1,
                    StaffWorkingHourDayDetailDTO::setMonday,
                    (byte) 2,
                    StaffWorkingHourDayDetailDTO::setTuesday,
                    (byte) 3,
                    StaffWorkingHourDayDetailDTO::setWednesday,
                    (byte) 4,
                    StaffWorkingHourDayDetailDTO::setThursday,
                    (byte) 5,
                    StaffWorkingHourDayDetailDTO::setFriday,
                    (byte) 6,
                    StaffWorkingHourDayDetailDTO::setSaturday);

    private static final Map<Integer, Function<BusinessWorkingHourDayDetailDTO, List<TimeRangeDto>>>
            DAY_OF_WEEK_FOR_GET_BUSINESS = ImmutableMap.of(
                    0,
                    BusinessWorkingHourDayDetailDTO::getSunday,
                    1,
                    BusinessWorkingHourDayDetailDTO::getMonday,
                    2,
                    BusinessWorkingHourDayDetailDTO::getTuesday,
                    3,
                    BusinessWorkingHourDayDetailDTO::getWednesday,
                    4,
                    BusinessWorkingHourDayDetailDTO::getThursday,
                    5,
                    BusinessWorkingHourDayDetailDTO::getFriday,
                    6,
                    BusinessWorkingHourDayDetailDTO::getSaturday);

    private static final Map<Integer, Function<StaffWorkingHourDayDetailDTO, List<TimeRangeDto>>>
            DAY_OF_WEEK_FOR_GET_STAFF = ImmutableMap.of(
                    0,
                    StaffWorkingHourDayDetailDTO::getSunday,
                    1,
                    StaffWorkingHourDayDetailDTO::getMonday,
                    2,
                    StaffWorkingHourDayDetailDTO::getTuesday,
                    3,
                    StaffWorkingHourDayDetailDTO::getWednesday,
                    4,
                    StaffWorkingHourDayDetailDTO::getThursday,
                    5,
                    StaffWorkingHourDayDetailDTO::getFriday,
                    6,
                    StaffWorkingHourDayDetailDTO::getSaturday);

    private static final Map<Integer, Function<StaffWorkingHourDetailDTO, StaffWorkingHourDayDetailDTO>>
            WEEK_FOR_GET_STAFF = ImmutableMap.of(
                    0,
                    StaffWorkingHourDetailDTO::getFirstWeek,
                    1,
                    StaffWorkingHourDetailDTO::getSecondWeek,
                    2,
                    StaffWorkingHourDetailDTO::getThirdWeek,
                    3,
                    StaffWorkingHourDetailDTO::getForthWeek);

    private static final Map<DayOfWeek, BiConsumer<StaffWorkingHourDayDetailDTO, List<TimeRangeDto>>>
            GOOGLE_DAY_OF_WEEK_FOR_SET_STAFF = ImmutableMap.of(
                    DayOfWeek.SUNDAY,
                    StaffWorkingHourDayDetailDTO::setSunday,
                    DayOfWeek.MONDAY,
                    StaffWorkingHourDayDetailDTO::setMonday,
                    DayOfWeek.TUESDAY,
                    StaffWorkingHourDayDetailDTO::setTuesday,
                    DayOfWeek.WEDNESDAY,
                    StaffWorkingHourDayDetailDTO::setWednesday,
                    DayOfWeek.THURSDAY,
                    StaffWorkingHourDayDetailDTO::setThursday,
                    DayOfWeek.FRIDAY,
                    StaffWorkingHourDayDetailDTO::setFriday,
                    DayOfWeek.SATURDAY,
                    StaffWorkingHourDayDetailDTO::setSaturday);

    /**
     * 保存 working hour 数据
     *
     * @param staffWorkingHourDetailDTO
     */
    public void saveStaffWorkingHour(Long companyId, StaffWorkingHourDetailDTO staffWorkingHourDetailDTO) {
        Integer businessId = staffWorkingHourDetailDTO.getBusinessId();
        Integer staffId = staffWorkingHourDetailDTO.getStaffId();

        MoeStaffWorkingHour workingHour = new MoeStaffWorkingHour();
        workingHour.setCompanyId(companyId);
        workingHour.setBusinessId(businessId);
        workingHour.setStaffId(staffId);
        workingHour.setScheduleType(staffWorkingHourDetailDTO.getScheduleType());
        workingHour.setStartDate(staffWorkingHourDetailDTO.getStartDate().toString());
        workingHour.setEndDate(staffWorkingHourDetailDTO.getEndDate().toString());
        if (Objects.isNull(staffWorkingHourDetailDTO.getFirstWeek())) {
            workingHour.setFirstWeek(EMPTY_JSON);
        } else {
            workingHour.setFirstWeek(JsonUtil.toJson(staffWorkingHourDetailDTO.getFirstWeek()));
        }
        if (Objects.isNull(staffWorkingHourDetailDTO.getSecondWeek())) {
            workingHour.setSecondWeek(EMPTY_JSON);
        } else {
            workingHour.setSecondWeek(JsonUtil.toJson(staffWorkingHourDetailDTO.getSecondWeek()));
        }
        if (Objects.isNull(staffWorkingHourDetailDTO.getThirdWeek())) {
            workingHour.setThirdWeek(EMPTY_JSON);
        } else {
            workingHour.setThirdWeek(JsonUtil.toJson(staffWorkingHourDetailDTO.getThirdWeek()));
        }
        if (Objects.isNull(staffWorkingHourDetailDTO.getForthWeek())) {
            workingHour.setForthWeek(EMPTY_JSON);
        } else {
            workingHour.setForthWeek(JsonUtil.toJson(staffWorkingHourDetailDTO.getForthWeek()));
        }

        MoeStaffWorkingHour moeStaffWorkingHour =
                moeStaffWorkingHourMapper.selectByBusinessIdAndStaffId(businessId, staffId);
        if (Objects.isNull(moeStaffWorkingHour)) {
            moeStaffWorkingHourMapper.insertSelectiveOnDuplicateKey(workingHour);
            // sync new data
            ThreadPool.submit(() -> {
                var workingHourSetting = getStaffWorkingHour(workingHour);
                updateTimeDays(companyId, businessId.longValue(), staffId.longValue(), workingHourSetting);
            });
            return;
        }

        moeStaffWorkingHourMapper.updateByBusinessIdAndStaffId(workingHour);

        // sync new data
        ThreadPool.submit(() -> {
            var workingHourSetting = getStaffWorkingHour(workingHour);
            updateTimeDays(companyId, businessId.longValue(), staffId.longValue(), workingHourSetting);
        });
    }

    private Map<String, TimeDailySetting> getOBTimeHoursMap(Long companyId, Long businessId, Long staffId) {
        // 查找ob的设置，根据ob的配置，初始化staff的by slot配置
        var resp = obAvailabilitySettingService.getGroomingServiceAvailability(
                GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build());

        Map<String, TimeDailySetting> slotHourSettingsMap = new HashMap<>();

        var obTimeAvailabilityType = resp.getAvailability().getTimeAvailabilityType();
        if (obTimeAvailabilityType == TimeAvailabilityType.WORKING_HOUR) {
            var obAvailabilities = obsStaffAvailabilityService.getStaffAvailability(
                    com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setBusinessId(businessId)
                            .addStaffIdList(staffId)
                            .setAvailabilityType(AvailabilityType.BY_TIME)
                            .build());
            // 遍历OB的配置，将slotHourSetting的列表放入map中
            obAvailabilities.getStaffAvailabilityListList().forEach(obAvailability -> {
                var currentStaffId = obAvailability.getStaffId();
                obAvailability.getTimeAvailabilityDayListList().forEach(slotAvailabilityDay -> {
                    // 星期几
                    var dayOfWeek = slotAvailabilityDay.getDayOfWeek();
                    var key = getStaffSlotDayKey(currentStaffId, ScheduleType.ONE_WEEK, dayOfWeek);
                    slotHourSettingsMap.put(key, slotAvailabilityDay.getTimeDailySetting());
                });
            });
        }
        return slotHourSettingsMap;
    }

    @NotNull
    private static String getStaffSlotDayKey(
            long currentStaffId, ScheduleType scheduleType, com.google.type.DayOfWeek dayOfWeek) {
        return String.format("%d-%d-%d", currentStaffId, scheduleType.getNumber(), dayOfWeek.getNumber());
    }

    public void updateTimeDays(
            Long companyId, Long businessId, Long staffId, WorkingHoursSetting workingHourStartEndTimeMap) {
        // 查找ob slot相关的配置，如果ob配置的是by time，返回空map
        var slotHoursMap = getOBTimeHoursMap(companyId, businessId, staffId);

        initTimeDaysByStaffWorkingHoursAndOBSettings(
                companyId, businessId, staffId, workingHourStartEndTimeMap, slotHoursMap);
    }

    private void initTimeDaysByStaffWorkingHoursAndOBSettings(
            Long companyId,
            Long businessId,
            Long staffId,
            WorkingHoursSetting workingHoursSetting,
            Map<String, TimeDailySetting> obSlotHoursMap) {

        List<TimeAvailabilityDayDef> timeAvailabilityDayDefs = new ArrayList<>();

        // 遍历scheduleType的枚举，生成week的记录
        Arrays.stream(ScheduleType.values()).forEach(scheduleType -> {
            if (scheduleType == ScheduleType.SCHEDULE_TYPE_UNSPECIFIED || scheduleType == ScheduleType.UNRECOGNIZED) {
                return;
            }
            // 遍历weekday的记录
            Arrays.stream(DayOfWeek.values()).forEach(dayOfWeek -> {
                if (dayOfWeek == DayOfWeek.DAY_OF_WEEK_UNSPECIFIED || dayOfWeek == DayOfWeek.UNRECOGNIZED) {
                    return;
                }

                boolean isAvailable = false;
                var currentDayTimeMap = workingHoursSetting
                        .timeMap()
                        .getOrDefault(getSlotDayKey(scheduleType.getNumber(), dayOfWeek.getNumber()), List.of());
                if (!CollectionUtils.isEmpty(currentDayTimeMap)) {
                    isAvailable = true;
                }

                var builder = TimeAvailabilityDayDef.newBuilder()
                        .setScheduleType(scheduleType)
                        .setDayOfWeek(dayOfWeek)
                        .setIsAvailable(isAvailable);

                var slotDayKey = getStaffSlotDayKey(staffId, scheduleType, dayOfWeek);
                if (obSlotHoursMap.containsKey(slotDayKey)) {
                    // 说明ob有对应这天的配置
                    var slotHours = obSlotHoursMap.get(slotDayKey);
                    if (Objects.nonNull(slotHours)) {
                        // 生成对应的limit ids
                        var serviceLimitsList = slotHours.getLimit().getServiceLimitsList().stream()
                                .map(serviceLimitation -> {
                                    return BookingLimitationDef.ServiceLimitation.newBuilder()
                                            .addAllServiceIds(serviceLimitation.getServiceIdsList())
                                            .setCapacity(serviceLimitation.getCapacity())
                                            .setIsAllService(serviceLimitation.getIsAllService())
                                            .build();
                                })
                                .toList();
                        var petSizeLimitsList = slotHours.getLimit().getPetSizeLimitsList().stream()
                                .map(petSizeLimitation -> {
                                    return BookingLimitationDef.PetSizeLimitation.newBuilder()
                                            .addAllPetSizeIds(petSizeLimitation.getPetSizeIdsList())
                                            .setCapacity(petSizeLimitation.getCapacity())
                                            .setIsAllSize(petSizeLimitation.getIsAllSize())
                                            .build();
                                })
                                .toList();
                        var petBreedLimitsList = slotHours.getLimit().getPetBreedLimitsList().stream()
                                .map(petBreedLimitation -> {
                                    return BookingLimitationDef.PetBreedLimitation.newBuilder()
                                            .setPetTypeId(petBreedLimitation.getPetTypeId())
                                            .setIsAllBreed(petBreedLimitation.getIsAllBreed())
                                            .addAllBreedIds(petBreedLimitation.getBreedIdsList())
                                            .setCapacity(petBreedLimitation.getCapacity())
                                            .build();
                                })
                                .toList();
                        builder.setTimeDailySetting(TimeDailySettingDef.newBuilder()
                                .setLimit(BookingLimitationDef.newBuilder()
                                        .addAllServiceLimits(serviceLimitsList)
                                        .addAllPetSizeLimits(petSizeLimitsList)
                                        .addAllPetBreedLimits(petBreedLimitsList)
                                        .build())
                                .build());
                    }
                }

                var list = currentDayTimeMap.stream()
                        .map(timeMap -> {
                            return TimeHourSettingDef.newBuilder()
                                    .setStartTime(timeMap.get("startTime"))
                                    .setEndTime(timeMap.get("endTime"))
                                    .build();
                        })
                        .toList();
                builder.addAllTimeHourSettingList(list);

                timeAvailabilityDayDefs.add(builder.build());
            });
        });

        var build = StaffAvailabilityDef.newBuilder()
                .setStaffId(staffId)
                .setIsAvailable(true)
                .setTimeScheduleType(workingHoursSetting.scheduleType())
                .addAllTimeAvailabilityDayList(timeAvailabilityDayDefs)
                .build();

        staffService.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addStaffAvailabilityList(build)
                .build());
    }

    public record WorkingHoursSetting(
            ScheduleType scheduleType, LocalDate startDateByTime, Map<String, List<Map<String, Integer>>> timeMap) {}

    @NotNull
    public WorkingHoursSetting getStaffWorkingHour(MoeStaffWorkingHour staffTimeDaySetting) {
        Map<String, List<Map<String, Integer>>> timeMap = new HashMap<>();

        // 从by time取start date
        var startDateByTimeStr = staffTimeDaySetting.getStartDate();
        if (startDateByTimeStr == null) {
            startDateByTimeStr = LocalDate.now().toString();
        }
        LocalDate startDate = LocalDate.parse(startDateByTimeStr)
                .with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY));

        var scheduleTypeByte = staffTimeDaySetting.getScheduleType();
        var scheduleType = ScheduleType.forNumber(scheduleTypeByte);

        var firstWeek = staffTimeDaySetting.getFirstWeek();
        var firstWeekJson = decodeWeekJson(firstWeek);
        timeWeeklyJsonPutDayTime2Map(1, firstWeekJson, timeMap);

        var secondWeek = staffTimeDaySetting.getSecondWeek();
        var secondWeekJson = decodeWeekJson(secondWeek);
        timeWeeklyJsonPutDayTime2Map(2, secondWeekJson, timeMap);

        var thirdWeek = staffTimeDaySetting.getThirdWeek();
        var thirdWeekJson = decodeWeekJson(thirdWeek);
        timeWeeklyJsonPutDayTime2Map(3, thirdWeekJson, timeMap);

        var fourthWeek = staffTimeDaySetting.getForthWeek();
        var fourthWeekJson = decodeWeekJson(fourthWeek);
        timeWeeklyJsonPutDayTime2Map(4, fourthWeekJson, timeMap);

        return new WorkingHoursSetting(scheduleType, startDate, timeMap);
    }

    private static void timeWeeklyJsonPutDayTime2Map(
            int weekIndex,
            Map<String, List<Map<String, Integer>>> weeklyJson,
            Map<String, List<Map<String, Integer>>> timeDayMap) {
        for (Map.Entry<String, List<Map<String, Integer>>> entry : weeklyJson.entrySet()) {
            var weekDay = entry.getKey(); // monday
            if (Objects.isNull(entry.getValue())) {
                timeDayMap.put(
                        // week+weekDay
                        getSlotDayKey(weekIndex, weekday2Index(weekDay)), List.of());
                continue;
            }
            timeDayMap.put(
                    // week+weekDay
                    getSlotDayKey(weekIndex, weekday2Index(weekDay)), entry.getValue());
        }
    }

    private static int weekday2Index(String weekDayName) {
        // 将输入的星期名称转换为小写
        String lowerCaseWeekDayName = weekDayName.toLowerCase();
        return switch (lowerCaseWeekDayName) {
            case "monday" -> 1;
            case "tuesday" -> 2;
            case "wednesday" -> 3;
            case "thursday" -> 4;
            case "friday" -> 5;
            case "saturday" -> 6;
            case "sunday" -> 7;
            default -> -1;
        };
    }

    @NotNull
    private static String getSlotDayKey(Integer scheduleType, Integer dayOfWeek) {
        return String.format("%d-%d", scheduleType, dayOfWeek);
    }

    public static Map<String, List<Map<String, Integer>>> decodeWeekJson(String jsonString) {
        Gson gson = new Gson();
        TypeToken<Map<String, List<Map<String, Integer>>>> typeToken = new TypeToken<>() {};
        return gson.fromJson(jsonString, typeToken.getType());
    }

    public void initStaffWorkingHour(Long companyId, Integer businessId, Integer staffId) {
        // 获取商家的 working hour
        BusinessWorkingHourDetailDTO businessWorkingHourDetail =
                businessWorkingHourService.getBusinessWorkingHourDetail(businessId);
        BusinessWorkingHourDayDetailDTO timeData = businessWorkingHourDetail.getTimeData();
        initStaffWorkingHour(companyId, businessId, staffId, timeData);
    }

    /**
     * 初始化 working hour 数据
     *
     * @param businessId
     * @param staffId
     * @param timeData
     */
    public void initStaffWorkingHour(
            Long companyId, Integer businessId, Integer staffId, BusinessWorkingHourDayDetailDTO timeData) {
        String businessTimeData = JsonUtil.toJson(timeData);

        MoeStaffWorkingHour workingHour = new MoeStaffWorkingHour();
        workingHour.setCompanyId(companyId);
        workingHour.setBusinessId(businessId);
        workingHour.setStaffId(staffId);
        workingHour.setScheduleType(NORMAL_SCHEDULE_TYPE);
        // business 的 create time 作为 startDate
        workingHour.setStartDate(DateUtil.getStringDate(
                moeBusinessMapper.selectByPrimaryKey(businessId).getCreateTime()));
        workingHour.setEndDate(ENDLESS_DATE);
        workingHour.setFirstWeek(businessTimeData);
        workingHour.setSecondWeek(businessTimeData);
        workingHour.setThirdWeek(businessTimeData);
        workingHour.setForthWeek(businessTimeData);

        MoeStaffWorkingHour moeStaffWorkingHour =
                moeStaffWorkingHourMapper.selectByBusinessIdAndStaffId(businessId, staffId);
        if (Objects.isNull(moeStaffWorkingHour)) {
            moeStaffWorkingHourMapper.insertSelectiveOnDuplicateKey(workingHour);
            // 保存OB Staff Time
            ThreadPool.execute(() -> {
                MoeBookOnlineStaffTimeParams obStaffTime = new MoeBookOnlineStaffTimeParams();
                obStaffTime.setStaffId(staffId);
                // 根据每天的配置时间，设置staffTime
                Map<String, StaffTime> resultStaffTime = getStaffTimeMap(timeData);
                obStaffTime.setStaffTimes(JsonUtil.toJson(resultStaffTime));
                groomingOnlineBookingClient.saveAvailableStaffTime(businessId, companyId, obStaffTime);

                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setBusinessId(businessId);
                staffDto.setCompanyId(companyId.intValue());
                staffDto.setId(staffId);
                staffDto.setBookOnlineAvailable(CommonConstant.ENABLE);
                groomingOnlineBookingClient.initializeAvailableStaffV2(businessId, List.of(staffDto));

                var workingHourSetting = getStaffWorkingHour(workingHour);
                updateTimeDays(companyId, businessId.longValue(), staffId.longValue(), workingHourSetting);
            });
        }
    }

    /**
     * 获取部分 staff 的 working hour
     *
     * @param businessId
     * @return
     */
    public Map<Integer, StaffWorkingHourDetailDTO> getStaffWorkingHourDetail(
            Integer businessId, List<Integer> staffIdList) {
        return getStaffTimeSlotDetail(businessId, staffIdList, AvailabilityType.BY_TIME);

        // Map<Integer, MoeStaffWorkingHour> workingHourMap =
        //         moeStaffWorkingHourMapper.queryByBusiness(businessId, staffIdList).stream()
        //                 .collect(
        //                         Collectors.toMap(MoeStaffWorkingHour::getStaffId, Function.identity(), (k1, k2) ->
        // k2));
        //
        // Map<Integer, StaffWorkingHourDetailDTO> result = new HashMap<>();
        //
        // BusinessWorkingHourDetailDTO businessWorkingHourDetail =
        //         businessWorkingHourService.getBusinessWorkingHourDetail(businessId);
        // BusinessWorkingHourDayDetailDTO timeData = businessWorkingHourDetail.getTimeData();
        //
        // MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        // staffIdList.forEach(staffId -> {
        //     MoeStaffWorkingHour workingHour = workingHourMap.get(staffId);
        //     if (Objects.isNull(workingHour)) {
        //         // todo 优化，批量初始化
        //         initStaffWorkingHour(migrateInfo.companyId(), businessId, staffId, timeData);
        //         workingHour = moeStaffWorkingHourMapper.selectByBusinessIdAndStaffId(businessId, staffId);
        //     }
        //     result.put(staffId, getStaffWorkingHourDetailDTO(workingHour));
        // });
        // return result;
    }

    public Map<Integer, StaffWorkingHourDetailDTO> getStaffTimeSlotDetail(
            Integer businessId, List<Integer> staffIdList, final AvailabilityType availabilityType) {
        var companyId = moeBusinessMapper.getCompanyIdByBusinessId(businessId);
        return staffService
                .getStaffAvailability(GetStaffAvailabilityRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllStaffIdList(
                                staffIdList.stream().map(Long::valueOf).toList())
                        .setAvailabilityType(availabilityType)
                        .build())
                .getStaffAvailabilityListList()
                .stream()
                .map(availability -> {
                    var staffWorkingHourDetailDTO = new StaffWorkingHourDetailDTO();
                    staffWorkingHourDetailDTO.setBusinessId(businessId);
                    staffWorkingHourDetailDTO.setStaffId(Math.toIntExact(availability.getStaffId()));
                    if (AvailabilityType.BY_TIME.equals(availabilityType)) {
                        staffWorkingHourDetailDTO.setScheduleType(
                                (byte) availability.getTimeScheduleType().getNumber());
                        staffWorkingHourDetailDTO.setStartDate(LocalDate.parse(availability.getTimeStartSunday()));
                        staffWorkingHourDetailDTO.setFirstWeek(
                                getTimeDayDetailDTO(availability, ScheduleType.ONE_WEEK));
                        staffWorkingHourDetailDTO.setSecondWeek(
                                getTimeDayDetailDTO(availability, ScheduleType.TWO_WEEK));
                        staffWorkingHourDetailDTO.setThirdWeek(
                                getTimeDayDetailDTO(availability, ScheduleType.THREE_WEEK));
                        staffWorkingHourDetailDTO.setForthWeek(
                                getTimeDayDetailDTO(availability, ScheduleType.FOUR_WEEK));
                    } else {
                        staffWorkingHourDetailDTO.setScheduleType(
                                (byte) availability.getScheduleType().getNumber());
                        staffWorkingHourDetailDTO.setStartDate(LocalDate.parse(availability.getSlotStartSunday()));
                        staffWorkingHourDetailDTO.setFirstWeek(
                                getSlotDayDetailDTO(availability, ScheduleType.ONE_WEEK));
                        staffWorkingHourDetailDTO.setSecondWeek(
                                getSlotDayDetailDTO(availability, ScheduleType.TWO_WEEK));
                        staffWorkingHourDetailDTO.setThirdWeek(
                                getSlotDayDetailDTO(availability, ScheduleType.THREE_WEEK));
                        staffWorkingHourDetailDTO.setForthWeek(
                                getSlotDayDetailDTO(availability, ScheduleType.FOUR_WEEK));
                    }
                    staffWorkingHourDetailDTO.setEndDate(LocalDate.parse(ENDLESS_DATE));
                    return staffWorkingHourDetailDTO;
                })
                .collect(Collectors.toMap(StaffWorkingHourDetailDTO::getStaffId, Function.identity(), (k1, k2) -> k2));
    }

    @Nonnull
    private static StaffWorkingHourDayDetailDTO getSlotDayDetailDTO(
            final StaffAvailability availability, ScheduleType scheduleType) {
        StaffWorkingHourDayDetailDTO staffWorkingHourDayDetailDTO = new StaffWorkingHourDayDetailDTO();
        availability.getSlotAvailabilityDayListList().stream()
                .filter(day -> scheduleType.equals(day.getScheduleType()))
                .forEach(day -> GOOGLE_DAY_OF_WEEK_FOR_SET_STAFF
                        .get(day.getDayOfWeek())
                        .accept(staffWorkingHourDayDetailDTO, convertToTimeRanges(day)));
        return staffWorkingHourDayDetailDTO;
    }

    @Nonnull
    private static StaffWorkingHourDayDetailDTO getTimeDayDetailDTO(
            final StaffAvailability availability, ScheduleType scheduleType) {
        StaffWorkingHourDayDetailDTO staffWorkingHourDayDetailDTO = new StaffWorkingHourDayDetailDTO();
        availability.getTimeAvailabilityDayListList().stream()
                .filter(day -> scheduleType.equals(day.getScheduleType()))
                .forEach(day -> GOOGLE_DAY_OF_WEEK_FOR_SET_STAFF
                        .get(day.getDayOfWeek())
                        .accept(staffWorkingHourDayDetailDTO, convertToTimeRanges(day)));
        return staffWorkingHourDayDetailDTO;
    }

    @Nonnull
    private static List<TimeRangeDto> convertToTimeRanges(SlotAvailabilityDay slotAvailabilityDay) {
        if (!slotAvailabilityDay.getIsAvailable()) {
            return List.of();
        }
        return List.of(TimeRangeDto.builder()
                .startTime(slotAvailabilityDay.getSlotDailySetting().getStartTime())
                .endTime(slotAvailabilityDay.getSlotDailySetting().getEndTime())
                .build());
    }

    @Nonnull
    private static List<TimeRangeDto> convertToTimeRanges(TimeAvailabilityDay timeAvailabilityDay) {
        if (!timeAvailabilityDay.getIsAvailable()) {
            return List.of();
        }
        return timeAvailabilityDay.getTimeHourSettingListList().stream()
                .map(timeHourSetting -> new TimeRangeDto(timeHourSetting.getStartTime(), timeHourSetting.getEndTime()))
                .toList();
    }

    /**
     * 获取单个 staff 的 working hour
     *
     * @param businessId
     * @param staffId
     * @return
     */
    public StaffWorkingHourDetailDTO getStaffWorkingHourDetail(Long companyId, Integer businessId, Integer staffId) {
        MoeStaffWorkingHour workingHour = moeStaffWorkingHourMapper.selectByBusinessIdAndStaffId(businessId, staffId);
        if (Objects.isNull(workingHour)) {
            initStaffWorkingHour(companyId, businessId, staffId);
            workingHour = moeStaffWorkingHourMapper.selectByBusinessIdAndStaffId(businessId, staffId);
        }

        return getStaffWorkingHourDetailDTO(workingHour);
    }

    public StaffWorkingHourDetailDTO getStaffWorkingHourDetailV2(Long companyId, Integer businessId, Integer staffId) {
        var staffWorkingHourDetailDTO = getStaffTimeSlotDetail(businessId, List.of(staffId), AvailabilityType.BY_TIME)
                .get(staffId);
        if (Objects.isNull(staffWorkingHourDetailDTO)) {
            // 获取商家的 working hour
            BusinessWorkingHourDetailDTO businessWorkingHourDetail =
                    businessWorkingHourService.getBusinessWorkingHourDetail(businessId);
            BusinessWorkingHourDayDetailDTO timeData = businessWorkingHourDetail.getTimeData();

            String businessTimeData = JsonUtil.toJson(timeData);
            MoeStaffWorkingHour workingHour = new MoeStaffWorkingHour();
            workingHour.setCompanyId(companyId);
            workingHour.setBusinessId(businessId);
            workingHour.setStaffId(staffId);
            workingHour.setScheduleType(NORMAL_SCHEDULE_TYPE);
            // business 的 create time 作为 startDate
            workingHour.setStartDate(DateUtil.getStringDate(
                    moeBusinessMapper.selectByPrimaryKey(businessId).getCreateTime()));
            workingHour.setEndDate(ENDLESS_DATE);
            workingHour.setFirstWeek(businessTimeData);
            workingHour.setSecondWeek(businessTimeData);
            workingHour.setThirdWeek(businessTimeData);
            workingHour.setForthWeek(businessTimeData);

            return getStaffWorkingHourDetailDTO(workingHour);
        }
        return staffWorkingHourDetailDTO;
    }

    public StaffWorkingHourDetailDTO getStaffWorkingHourDetailDTO(MoeStaffWorkingHour workingHour) {
        StaffWorkingHourDetailDTO detailDTO = new StaffWorkingHourDetailDTO();
        detailDTO.setBusinessId(workingHour.getBusinessId());
        detailDTO.setStaffId(workingHour.getStaffId());
        detailDTO.setScheduleType(workingHour.getScheduleType());
        detailDTO.setStartDate(LocalDate.parse(workingHour.getStartDate()));
        detailDTO.setEndDate(LocalDate.parse(workingHour.getEndDate()));
        detailDTO.setFirstWeek(JsonUtil.toBean(workingHour.getFirstWeek(), StaffWorkingHourDayDetailDTO.class));
        detailDTO.setSecondWeek(JsonUtil.toBean(workingHour.getSecondWeek(), StaffWorkingHourDayDetailDTO.class));
        detailDTO.setThirdWeek(JsonUtil.toBean(workingHour.getThirdWeek(), StaffWorkingHourDayDetailDTO.class));
        detailDTO.setForthWeek(JsonUtil.toBean(workingHour.getForthWeek(), StaffWorkingHourDayDetailDTO.class));

        return detailDTO;
    }

    private Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffTimeSlot(
            Integer businessId,
            List<Integer> staffIdList,
            String startDate,
            String endDate,
            final AvailabilityType availabilityType) {
        if (Objects.isNull(businessId)
                || staffIdList.isEmpty()
                || Objects.isNull(startDate)
                || Objects.isNull(endDate)) {
            return Collections.emptyMap();
        }

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        List<LocalDate> dateRange = Stream.iterate(start, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(start, end) + 1)
                .toList();

        return getStaffTimeSlot(businessId, staffIdList, dateRange, availabilityType);
    }

    /**
     * 获取部份 staff 在一段时间内的 working hour
     *
     * @param businessId
     * @param staffIdList
     * @param dateRange
     * @return
     */
    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWorkingHour(
            Integer businessId, List<Integer> staffIdList, List<LocalDate> dateRange) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(staffIdList) || CollectionUtils.isEmpty(dateRange)) {
            return Collections.emptyMap();
        }

        List<LocalDate> dateRangeList = new ArrayList<>(dateRange);
        Collections.sort(dateRangeList);

        // 查询商家的 working hour
        BusinessWorkingHourDetailDTO businessWorkingHourDetail =
                businessWorkingHourService.getBusinessWorkingHourDetail(businessId);
        BusinessWorkingHourDayDetailDTO businessTimeData = businessWorkingHourDetail.getTimeData();
        // 查询员工的 working hour
        Map<Integer, StaffWorkingHourDetailDTO> staffWorkingHourDetailMap =
                getStaffWorkingHourDetail(businessId, staffIdList);

        // 结果集
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result = new ConcurrentHashMap<>(32);

        // 并行查询
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        staffIdList.forEach(staffId -> completableFutureList.add(CompletableFuture.runAsync(
                () -> result.put(
                        staffId,
                        getEveryStaffWorkingHour(
                                dateRangeList, businessTimeData, staffWorkingHourDetailMap.get(staffId))),
                ThreadPool.getExecutor())));

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();

        return result;
    }

    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffTimeSlot(
            Integer businessId,
            List<Integer> staffIdList,
            List<LocalDate> dateRange,
            final AvailabilityType availabilityType) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(staffIdList) || CollectionUtils.isEmpty(dateRange)) {
            return Collections.emptyMap();
        }

        List<LocalDate> dateRangeList = new ArrayList<>(dateRange);
        Collections.sort(dateRangeList);

        // 查询商家的 working hour
        BusinessWorkingHourDetailDTO businessWorkingHourDetail =
                businessWorkingHourService.getBusinessWorkingHourDetail(businessId);
        BusinessWorkingHourDayDetailDTO businessTimeData = businessWorkingHourDetail.getTimeData();
        // 查询员工的 working hour
        Map<Integer, StaffWorkingHourDetailDTO> staffWorkingHourDetailMap =
                getStaffTimeSlotDetail(businessId, staffIdList, availabilityType);

        // 结果集
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result = new ConcurrentHashMap<>(32);

        // 并行查询
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        staffIdList.forEach(staffId -> completableFutureList.add(CompletableFuture.runAsync(
                () -> result.put(
                        staffId,
                        getEveryStaffWorkingHour(
                                dateRangeList, businessTimeData, staffWorkingHourDetailMap.get(staffId))),
                ThreadPool.getSubmitExecutor())));

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();

        return result;
    }

    private static Map<LocalDate, List<TimeRangeDto>> getEveryStaffWorkingHour(
            List<LocalDate> localDateRange,
            BusinessWorkingHourDayDetailDTO businessTimeData,
            StaffWorkingHourDetailDTO staffWorkingHourDetail) {
        LocalDate workingHourStartDate = staffWorkingHourDetail.getStartDate();
        LocalDate workingHourEndDate = staffWorkingHourDetail.getEndDate();

        Map<LocalDate, List<TimeRangeDto>> staffResult = new ConcurrentHashMap<>(32);

        int rangeSize = localDateRange.size();
        LocalDate dateRangeStart = localDateRange.get(0);
        LocalDate dateRangeEnd = localDateRange.get(rangeSize - 1);

        // 如果 date 不在设置的时间范围内，则使用商家的设置
        if (workingHourEndDate.isBefore(dateRangeStart) || workingHourStartDate.isAfter(dateRangeEnd)) {
            localDateRange.forEach(currentLocalDate -> {
                // 默认用商家的设置
                List<TimeRangeDto> staffTimeData = getBusinessTimeDataByCurrentDate(businessTimeData, currentLocalDate);
                staffResult.put(currentLocalDate, staffTimeData);
            });
            return staffResult;
        }

        int startDateIndex = Collections.binarySearch(localDateRange, workingHourStartDate);
        if (startDateIndex < 0) {
            startDateIndex = 0;
        }
        int endDateIndex = Collections.binarySearch(localDateRange, workingHourEndDate);
        if (endDateIndex < 0) {
            endDateIndex = rangeSize;
        }

        localDateRange.subList(0, startDateIndex).forEach(currentDate -> {
            // 默认用商家的设置
            List<TimeRangeDto> staffTimeData = getBusinessTimeDataByCurrentDate(businessTimeData, currentDate);
            staffResult.put(currentDate, staffTimeData);
        });
        // 偏移计算
        int endDateIndexReal = endDateIndex == rangeSize ? rangeSize : endDateIndex + 1;
        localDateRange.subList(startDateIndex, endDateIndexReal).forEach(currentDate -> {
            List<TimeRangeDto> staffTimeData = getStaffTimeDataByCurrentDate(staffWorkingHourDetail, currentDate);
            staffResult.put(currentDate, staffTimeData);
        });
        localDateRange.subList(endDateIndexReal, rangeSize).forEach(currentDate -> {
            // 默认用商家的设置
            List<TimeRangeDto> staffTimeData = getBusinessTimeDataByCurrentDate(businessTimeData, currentDate);
            staffResult.put(currentDate, staffTimeData);
        });

        return staffResult;
    }

    private static List<TimeRangeDto> getBusinessTimeDataByCurrentDate(
            BusinessWorkingHourDayDetailDTO businessTimeData, LocalDate currentDate) {
        List<TimeRangeDto> workingHour = DAY_OF_WEEK_FOR_GET_BUSINESS
                .get(DateUtil.localDateToWeek(currentDate))
                .apply(businessTimeData);
        return workingHour == null ? List.of() : workingHour;
    }

    static List<TimeRangeDto> getStaffTimeDataByCurrentDate(
            StaffWorkingHourDetailDTO staffWorkingHourDetail, LocalDate currentLocalDate) {
        LocalDate workingHourStartDate = staffWorkingHourDetail.getStartDate();
        Byte scheduleType = staffWorkingHourDetail.getScheduleType();
        int dayOfWeek = DateUtil.localDateToWeek(currentLocalDate);

        // 如果设置为每周相同时间，则直接按星期获取时间数据
        if (Objects.equals(scheduleType, NORMAL_SCHEDULE_TYPE) || scheduleType < 1) {
            List<TimeRangeDto> workingHour =
                    DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek).apply(staffWorkingHourDetail.getFirstWeek());
            return workingHour == null ? List.of() : workingHour;
        }

        // 计算 startDate 所在星期的星期日，也就是绝对开始时间
        LocalDate absoluteStartDate = workingHourStartDate.minusDays(DateUtil.localDateToWeek(workingHourStartDate));
        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(absoluteStartDate, currentLocalDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, WEEK_PERIOD);
        // 计算循环中第几周，0 是第一周，1 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType));

        List<TimeRangeDto> workingHour = WEEK_FOR_GET_STAFF
                .get(weekNumber)
                .andThen(DAY_OF_WEEK_FOR_GET_STAFF.get(dayOfWeek))
                .apply(staffWorkingHourDetail);
        return workingHour == null ? List.of() : workingHour;
    }

    /**
     * 获取部份 staff 在一段时间内的 working hour，使用 override date 覆盖
     *
     * @param businessId
     * @param staffIdList
     * @param startDate
     * @param endDate
     * @return
     */
    // public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWorkingHourWithOverrideDate(
    //         Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
    //     return getStaffTimeSlotWithOverrideDate(businessId, staffIdList, startDate, endDate,
    // AvailabilityType.BY_TIME);
    //
    //     // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffWorkingHour =
    //     //         getStaffWorkingHour(businessId, staffIdList, startDate, endDate);
    //     //
    //     // Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> staffOverrideDetailMap =
    //     //         staffOverrideDateService.getStaffOverrideDateDetailMap(businessId, startDate, endDate);
    //     // if (staffOverrideDetailMap.isEmpty()) {
    //     //     return staffWorkingHour;
    //     // }
    //     //
    //     // staffWorkingHour.forEach((staffId, dateListMap) -> {
    //     //     dateListMap.keySet().forEach(localDate -> {
    //     //         if (staffOverrideDetailMap.containsKey(Pair.of(staffId, localDate))) {
    //     //             // 使用 override localDate 覆盖
    //     //             dateListMap.put(localDate, staffOverrideDetailMap.get(Pair.of(staffId, localDate)));
    //     //         }
    //     //     });
    //     // });
    //     //
    //     // return staffWorkingHour;
    // }
    private Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffTimeSlotWithOverrideDate(
            Integer businessId,
            List<Integer> staffIdList,
            String startDate,
            String endDate,
            final AvailabilityType availabilityType) {
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffTimeSlot =
                getStaffTimeSlot(businessId, staffIdList, startDate, endDate, availabilityType);

        Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> staffOverrideDetailMap =
                getStaffTimeSlotOverrideDateDetailMap(businessId, startDate, endDate, staffIdList, availabilityType);
        if (staffOverrideDetailMap.isEmpty()) {
            return staffTimeSlot;
        }

        staffTimeSlot.forEach((staffId, dateListMap) -> dateListMap.keySet().forEach(localDate -> {
            if (staffOverrideDetailMap.containsKey(Pair.of(staffId, localDate))) {
                // 使用 override localDate 覆盖
                dateListMap.put(localDate, staffOverrideDetailMap.get(Pair.of(staffId, localDate)));
            }
        }));

        return staffTimeSlot;
    }

    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWithOverrideDate(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {

        var companyId = moeBusinessMapper.getCompanyIdByBusinessId(businessId);
        var availabilityType = staffService
                .getBusinessStaffAvailabilityType(
                        com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                .getAvailabilityType();

        return getStaffTimeSlotWithOverrideDate(businessId, staffIdList, startDate, endDate, availabilityType);
    }

    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWithOverrideDateAndClosedDate(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {

        var staffWithOverrideDate = getStaffWithOverrideDate(businessId, staffIdList, startDate, endDate);

        // 获取business的closed date & 提前查出来那些date不可用
        List<MoeBusinessCloseDate> allCloseDate =
                businessCloseDateService.getCloseDateByStartDateEndDate(businessId, startDate, endDate);
        List<LocalDate> localDateRange = Stream.iterate(LocalDate.parse(startDate), date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(LocalDate.parse(startDate), LocalDate.parse(endDate)) + 1)
                .toList();
        List<LocalDate> noAvailableDate = businessCloseDateService.getNoAvailableDate(localDateRange, allCloseDate);

        staffWithOverrideDate.forEach((staffId, dateListMap) -> {
            dateListMap.keySet().forEach(localDate -> {
                if (noAvailableDate.contains(localDate)) {
                    dateListMap.put(localDate, List.of());
                }
            });
        });

        return staffWithOverrideDate;
    }

    public Map<Integer, Map<String, List<TimeRangeDto>>> getStaffWithOverrideDateAndClosedDateString(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        var staffWithOverrideDate = getStaffWithOverrideDate(businessId, staffIdList, startDate, endDate);

        return staffWithOverrideDate.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue().entrySet().stream()
                        .collect(Collectors.toMap(entry2 -> entry2.getKey().toString(), entry2 -> entry2.getValue()))));
    }

    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWithOverrideDateAndClosedDateForCalendarView(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {

        Map<String, Set<Integer>> staffIdListAssignedAppointment =
                iGroomingAppointmentClient.getStaffAssignedAppointmentByDate(businessId, startDate, endDate);
        var hasAppointmentStaffIds = staffIdListAssignedAppointment.values().stream()
                .flatMap(Collection::stream)
                .toList();
        var newStaffIdList = Stream.of(staffIdList, hasAppointmentStaffIds)
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        return getStaffWithOverrideDateAndClosedDate(businessId, newStaffIdList, startDate, endDate);
    }

    public List<StaffByWorkingDateVO> getStaffListByWorkingDateAndStaffIdList(
            Integer businessId, final List<Integer> staffIdList, String startDate, String endDate) {
        var staffAvailability =
                getStaffWithOverrideDateAndClosedDateForCalendarView(businessId, staffIdList, startDate, endDate);

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        return Stream.iterate(start, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(start, end) + 1)
                .map(date -> {
                    String dateStr = date.toString();
                    StaffByWorkingDateVO staffByWorkingDateVO = new StaffByWorkingDateVO();
                    staffByWorkingDateVO.setDate(dateStr);
                    staffByWorkingDateVO.setStaffs(staffAvailability.entrySet().stream()
                            .map(entry -> {
                                return new StaffWithWorkingRangeVO(
                                        entry.getKey(), entry.getValue().getOrDefault(date, List.of()));
                            })
                            .toList());
                    return staffByWorkingDateVO;
                })
                .toList();
    }

    public Map<Integer, List<String>> getStaffOverrideDateList(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        var companyId = moeBusinessMapper.getCompanyIdByBusinessId(businessId);
        var availabilityType = staffService
                .getBusinessStaffAvailabilityType(
                        com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                .getAvailabilityType();

        Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> staffOverrideDetailMap =
                getStaffTimeSlotOverrideDateDetailMap(businessId, startDate, endDate, staffIdList, availabilityType);
        if (staffOverrideDetailMap.isEmpty()) {
            return Map.of();
        }

        Map<Integer, List<String>> result = new HashMap<>();
        staffOverrideDetailMap.keySet().forEach(pair -> result.computeIfAbsent(pair.getLeft(), k -> new ArrayList<>())
                .add(pair.getRight().toString()));
        return result;
    }

    public Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> getStaffTimeSlotOverrideDateDetailMap(
            Integer businessId,
            String startDate,
            String endDate,
            List<Integer> staffIdList,
            final AvailabilityType availabilityType) {
        var companyId = moeBusinessMapper.getCompanyIdByBusinessId(businessId);
        var staffAvailabilityOverride =
                staffService.getStaffAvailabilityOverride(GetStaffAvailabilityOverrideRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllStaffIds(staffIdList.stream().map(Long::valueOf).toList())
                        .setAvailabilityType(availabilityType)
                        .build());
        if (AvailabilityType.BY_TIME.equals(availabilityType)) {
            return staffAvailabilityOverride.getTimeOverrideDaysMap().entrySet().stream()
                    .collect(Collectors.toMap(
                            staffAvailability -> Math.toIntExact(staffAvailability.getKey()),
                            staffAvailability -> staffAvailability.getValue().getSlotsList().stream()
                                    .filter(slot -> slot.getOverrideDate().compareTo(startDate) >= 0
                                            && slot.getOverrideDate().compareTo(endDate) <= 0)
                                    .map(availability -> {
                                        var staffWorkingHourDetailDTO = new StaffOverrideDateDetailDTO();
                                        staffWorkingHourDetailDTO.setBusinessId(businessId);
                                        staffWorkingHourDetailDTO.setStaffId(
                                                Math.toIntExact(availability.getStaffId()));
                                        staffWorkingHourDetailDTO.setOverrideDate(
                                                LocalDate.parse(availability.getOverrideDate()));
                                        if (!availability.getIsAvailable()) {
                                            staffWorkingHourDetailDTO.setTimeData(List.of());
                                        } else {
                                            staffWorkingHourDetailDTO.setTimeData(convertToTimeRanges(availability));
                                        }
                                        return staffWorkingHourDetailDTO;
                                    })
                                    .toList()))
                    .values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toMap(
                            k -> Pair.of(k.getStaffId(), k.getOverrideDate()),
                            StaffOverrideDateDetailDTO::getTimeData,
                            (v1, v2) -> v2));
        }
        return staffAvailabilityOverride.getOverrideDaysMap().entrySet().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> Math.toIntExact(staffAvailability.getKey()),
                        staffAvailability -> staffAvailability.getValue().getSlotsList().stream()
                                .filter(slot -> slot.getOverrideDate().compareTo(startDate) >= 0
                                        && slot.getOverrideDate().compareTo(endDate) <= 0)
                                .map(availability -> {
                                    var staffWorkingHourDetailDTO = new StaffOverrideDateDetailDTO();
                                    staffWorkingHourDetailDTO.setBusinessId(businessId);
                                    staffWorkingHourDetailDTO.setStaffId(Math.toIntExact(availability.getStaffId()));
                                    staffWorkingHourDetailDTO.setOverrideDate(
                                            LocalDate.parse(availability.getOverrideDate()));
                                    if (!availability.getIsAvailable()) {
                                        staffWorkingHourDetailDTO.setTimeData(List.of());
                                    } else {
                                        staffWorkingHourDetailDTO.setTimeData(convertToTimeRanges(availability));
                                    }
                                    return staffWorkingHourDetailDTO;
                                })
                                .toList()))
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(
                        k -> Pair.of(k.getStaffId(), k.getOverrideDate()),
                        StaffOverrideDateDetailDTO::getTimeData,
                        (v1, v2) -> v2));
    }
}
