package com.moego.server.business.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeHourSetting;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityResponse;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapperbean.MoeStaffWorkingHour;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StaffWorkingHourServiceTest {

    @Mock
    BusinessWorkingHourService businessWorkingHourService;

    @InjectMocks
    StaffWorkingHourService staffWorkingHourService;

    @Mock
    private MoeBusinessMapper moeBusinessMapper;

    @Mock
    private com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    MoeStaffWorkingHour buildMoeStaffWorkingHourScheduleType2FirstWeekNotSameSecondWeek() {
        MoeStaffWorkingHour staffWorkingHour = new MoeStaffWorkingHour();
        staffWorkingHour.setBusinessId(10001);
        staffWorkingHour.setStaffId(20001);
        staffWorkingHour.setStartDate("2023-03-05");
        staffWorkingHour.setEndDate("9999-12-31");
        staffWorkingHour.setScheduleType((byte) 2);
        staffWorkingHour.setFirstWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setSecondWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [{\"endTime\": 900, \"startTime\": 495}], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setThirdWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [{\"endTime\": 900, \"startTime\": 495}], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setForthWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [{\"endTime\": 900, \"startTime\": 495}], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        return staffWorkingHour;
    }

    GetStaffAvailabilityResponse buildStaffAvailabilityScheduleType2FirstWeekNotSameSecondWeek() {
        return GetStaffAvailabilityResponse.newBuilder()
                .addStaffAvailabilityList(StaffAvailability.newBuilder()
                        .setStaffId(20001)
                        .setIsAvailable(true)
                        .setTimeScheduleType(ScheduleType.TWO_WEEK)
                        .addTimeAvailabilityDayList(TimeAvailabilityDay.newBuilder()
                                .setDayOfWeek(DayOfWeek.SATURDAY)
                                .setIsAvailable(true)
                                .setScheduleType(ScheduleType.ONE_WEEK)
                                .addTimeHourSettingList(TimeHourSetting.getDefaultInstance())
                                .setStaffId(20001)
                                .build())
                        .addTimeAvailabilityDayList(TimeAvailabilityDay.newBuilder()
                                .setDayOfWeek(DayOfWeek.SATURDAY)
                                .setIsAvailable(true)
                                .setScheduleType(ScheduleType.TWO_WEEK)
                                .addTimeHourSettingList(TimeHourSetting.newBuilder()
                                        .setStartTime(495)
                                        .setEndTime(1020)
                                        .build())
                                .setStaffId(20001)
                                .build())
                        .setTimeStartSunday("2023-03-05")
                        .build())
                .build();
    }

    MoeStaffWorkingHour buildMoeStaffWorkingHourScheduleType2FirstWeekSameWithSecondWeek() {
        MoeStaffWorkingHour staffWorkingHour = new MoeStaffWorkingHour();
        staffWorkingHour.setBusinessId(10001);
        staffWorkingHour.setStaffId(20001);
        staffWorkingHour.setStartDate("2023-03-05");
        staffWorkingHour.setEndDate("9999-12-31");
        staffWorkingHour.setScheduleType((byte) 2);
        staffWorkingHour.setFirstWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setSecondWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setThirdWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [{\"endTime\": 900, \"startTime\": 495}], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        staffWorkingHour.setForthWeek(
                "{\"friday\": [{\"endTime\": 1020, \"startTime\": 495}], \"monday\": [], \"sunday\": [], \"tuesday\": [{\"endTime\": 1020, \"startTime\": 495}], \"saturday\": [{\"endTime\": 900, \"startTime\": 495}], \"thursday\": [{\"endTime\": 1020, \"startTime\": 495}], \"wednesday\": [{\"endTime\": 1020, \"startTime\": 495}]}");
        return staffWorkingHour;
    }

    GetStaffAvailabilityResponse buildStaffAvailabilityScheduleType2FirstWeekSameWithSecondWeek() {
        return GetStaffAvailabilityResponse.newBuilder()
                .addStaffAvailabilityList(StaffAvailability.newBuilder()
                        .setStaffId(20001)
                        .setIsAvailable(true)
                        .setTimeScheduleType(ScheduleType.TWO_WEEK)
                        .addTimeAvailabilityDayList(TimeAvailabilityDay.newBuilder()
                                .setDayOfWeek(DayOfWeek.MONDAY)
                                .setIsAvailable(true)
                                .setScheduleType(ScheduleType.TWO_WEEK)
                                .addTimeHourSettingList(TimeHourSetting.newBuilder()
                                        .setStartTime(495)
                                        .setEndTime(1020)
                                        .build())
                                .setStaffId(20001)
                                .build())
                        .setTimeStartSunday("2023-03-05")
                        .build())
                .build();
    }

    @Test
    @DisplayName("Test get staff working hour, when schedule type is 2, first week not same second week on week 1")
    void testGetStaffWorkingHourScheduleType2FirstWeekNotSameSecondWeekOnWeek1() {
        when(businessWorkingHourService.getBusinessWorkingHourDetail(anyInt()))
                .thenReturn(new BusinessWorkingHourDetailDTO());
        when(moeBusinessMapper.getCompanyIdByBusinessId(anyInt())).thenReturn(10001);
        when(staffService.getStaffAvailability(any()))
                .thenReturn(buildStaffAvailabilityScheduleType2FirstWeekNotSameSecondWeek());

        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result =
                staffWorkingHourService.getStaffWorkingHour(10001, List.of(20001), List.of(LocalDate.of(2023, 6, 10)));

        assertThat(result).containsKey(20001);
        assertThat(result.get(20001)).containsKey(LocalDate.of(2023, 6, 10));
        assertThat(result.get(20001).get(LocalDate.of(2023, 6, 10))).isNotEmpty();
        assertThat(result.get(20001).get(LocalDate.of(2023, 6, 10)).get(0).getStartTime())
                .isEqualTo(495);
    }

    @Test
    @DisplayName("Test get staff working hour, when schedule type is 2, first week not same second week on week 2")
    void testGetStaffWorkingHourScheduleType2FirstWeekNotSameSecondWeekOnWeek2() {
        when(businessWorkingHourService.getBusinessWorkingHourDetail(anyInt()))
                .thenReturn(new BusinessWorkingHourDetailDTO());
        when(moeBusinessMapper.getCompanyIdByBusinessId(anyInt())).thenReturn(10001);
        when(staffService.getStaffAvailability(any()))
                .thenReturn(buildStaffAvailabilityScheduleType2FirstWeekSameWithSecondWeek());

        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result =
                staffWorkingHourService.getStaffWorkingHour(10001, List.of(20001), List.of(LocalDate.of(2023, 6, 17)));

        assertThat(result).containsKey(20001);
        assertThat(result.get(20001)).containsKey(LocalDate.of(2023, 6, 17));
        assertThat(result.get(20001).get(LocalDate.of(2023, 6, 17))).isEmpty();
    }

    @Test
    @DisplayName("Test get staff working hour, when schedule type is 2, first week same with second week on week 1")
    void testGetStaffWorkingHourScheduleType2FirstWeekSameWithSecondWeekOnWeek1() {
        when(businessWorkingHourService.getBusinessWorkingHourDetail(anyInt()))
                .thenReturn(new BusinessWorkingHourDetailDTO());
        when(moeBusinessMapper.getCompanyIdByBusinessId(anyInt())).thenReturn(10001);
        when(staffService.getStaffAvailability(any()))
                .thenReturn(buildStaffAvailabilityScheduleType2FirstWeekSameWithSecondWeek());

        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result =
                staffWorkingHourService.getStaffWorkingHour(10001, List.of(20001), List.of(LocalDate.of(2023, 6, 10)));

        assertThat(result).containsKey(20001);
        assertThat(result.get(20001)).containsKey(LocalDate.of(2023, 6, 10));
        assertThat(result.get(20001).get(LocalDate.of(2023, 6, 10))).isEmpty();
    }

    @Test
    @DisplayName("Test get staff working hour, when schedule type is 2, first week same with second week on week 2")
    void testGetStaffWorkingHourScheduleType2FirstWeekSameWithSecondWeekOnWeek2() {
        when(businessWorkingHourService.getBusinessWorkingHourDetail(anyInt()))
                .thenReturn(new BusinessWorkingHourDetailDTO());
        when(moeBusinessMapper.getCompanyIdByBusinessId(anyInt())).thenReturn(10001);
        when(staffService.getStaffAvailability(any()))
                .thenReturn(buildStaffAvailabilityScheduleType2FirstWeekSameWithSecondWeek());

        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> result =
                staffWorkingHourService.getStaffWorkingHour(10001, List.of(20001), List.of(LocalDate.of(2023, 6, 17)));

        assertThat(result).containsKey(20001);
        assertThat(result.get(20001)).containsKey(LocalDate.of(2023, 6, 17));
        assertThat(result.get(20001).get(LocalDate.of(2023, 6, 17))).isEmpty();
    }
}
